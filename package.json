{"name": "ivan-boxing", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "npm run collect-messages && next build", "start": "next start", "lint": "next lint", "intl": "node scripts/intl.js"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-slot": "^1.2.3", "@tabler/icons-react": "^3.34.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "lucide-react": "^0.539.0", "motion": "^12.23.12", "next": "15.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-intl": "^7.1.11", "react-social-media-embed": "^2.5.18", "swiper": "^11.2.10", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5", "typescript-react-intl": "^0.4.1"}}